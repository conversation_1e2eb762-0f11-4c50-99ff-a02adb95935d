// Test script to debug the nova sneakers scraping issue
// Run with: node test-nova-scraping.js

async function testNovaScraping() {
  const url = "https://olympuslondon.com/products/novasneakers";
  
  console.log("Testing nova sneakers scraping with URL:", url);
  
  try {
    // Extract product handle from URL
    const urlParts = new URL(url);
    const pathParts = urlParts.pathname.split('/');
    const handle = pathParts[pathParts.indexOf('products') + 1] || 'sample-product';
    
    console.log("Extracted handle:", handle);
    
    // Try Shopify product JSON endpoint
    const productUrl = `${urlParts.origin}/products/${handle}.js`;
    console.log("Attempting to fetch from JSON endpoint:", productUrl);
    
    try {
      const response = await fetch(productUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      console.log("JSON endpoint response status:", response.status);
      
      if (response.ok) {
        const productData = await response.json();
        console.log("Successfully fetched product data from .js endpoint:", {
          title: productData.title,
          price: productData.price,
          variants: productData.variants?.length,
          images: productData.images?.length,
          vendor: productData.vendor,
          type: productData.type
        });
        
        // Process the data like our application does
        const variants = productData.variants?.map((variant, index) => ({
          id: variant.id?.toString() || `var-${index}`,
          title: variant.title || 'Default Title',
          price: typeof variant.price === 'number' ? (variant.price / 100).toFixed(2) : 
                 typeof variant.price === 'string' ? variant.price : '0.00',
          sku: variant.sku || '',
          inventory_quantity: variant.inventory_quantity || 0,
          weight: variant.weight || 0,
          option1: variant.option1 || null,
          option2: variant.option2 || null,
          option3: variant.option3 || null
        })) || [];
        
        console.log("Processed variants:", variants.length, "first variant:", variants[0]);
        
        // Process images
        const images = productData.images?.map((img) => {
          let imageUrl = img;
          
          // Handle different URL formats
          if (imageUrl.startsWith('//')) {
            imageUrl = `https:${imageUrl}`;
          } else if (imageUrl.startsWith('/')) {
            imageUrl = `${urlParts.origin}${imageUrl}`;
          }
          
          // Remove size parameters to get high-quality images
          imageUrl = imageUrl.replace(/(_\d+x\d*|_\d*x\d+|_compact|_grande|_large|_medium|_small|_thumb)\.(jpg|jpeg|png|gif|webp)/i, '.$2');
          
          return imageUrl;
        }) || [];
        
        console.log("Processed images:", images.length, "first image:", images[0]);
        
        // Process price
        const mainPrice = typeof productData.price === 'number' ? 
          (productData.price / 100).toFixed(2) : 
          variants.length > 0 ? variants[0].price : '0.00';
        
        const compareAtPrice = productData.compare_at_price && typeof productData.compare_at_price === 'number' ? 
          (productData.compare_at_price / 100).toFixed(2) : undefined;
        
        const result = {
          handle: productData.handle || handle,
          title: productData.title || `Product ${handle}`,
          body_html: productData.description || '<p>No description available.</p>',
          vendor: productData.vendor || 'Unknown Vendor',
          type: productData.type || 'General',
          tags: Array.isArray(productData.tags) ? productData.tags : [],
          published: productData.available !== false,
          images: images.filter(img => img && img.length > 0),
          variants: variants.length > 0 ? variants : [{
            id: 'default-var',
            title: 'Default Title',
            price: mainPrice,
            sku: handle.toUpperCase(),
            inventory_quantity: 1,
            weight: 0
          }],
          price: mainPrice,
          compare_at_price: compareAtPrice
        };
        
        console.log("Final processed result:", {
          title: result.title,
          price: result.price,
          vendor: result.vendor,
          type: result.type,
          imageCount: result.images.length,
          variantCount: result.variants.length,
          published: result.published
        });
        
        return result;
        
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (jsonError) {
      console.error("Failed to fetch from .js endpoint:", jsonError);
      throw jsonError;
    }
    
  } catch (error) {
    console.error("Test failed:", error);
    return null;
  }
}

// Run the test
console.log("Starting nova sneakers scraping test...");
testNovaScraping().then(result => {
  if (result) {
    console.log("✅ Test completed successfully!");
    console.log("Result summary:", {
      title: result.title,
      price: result.price,
      vendor: result.vendor,
      images: result.images.length,
      variants: result.variants.length
    });
  } else {
    console.log("❌ Test failed!");
  }
}).catch(error => {
  console.error("❌ Test error:", error);
});
