// Mock data generator for demo purposes
// This simulates what the backend would return

interface ProductVariant {
  id: string;
  title: string;
  price: string;
  sku?: string;
  inventory_quantity?: number;
  weight?: number;
  option1?: string;
  option2?: string;
  option3?: string;
}

interface ProductData {
  handle: string;
  title: string;
  body_html: string;
  vendor: string;
  type: string;
  tags: string[];
  published: boolean;
  images: string[];
  variants: ProductVariant[];
  price: string;
  compare_at_price?: string;
}

export async function generateMockProductData(url: string): Promise<ProductData> {
  try {
    // Extract product handle from URL
    const urlParts = new URL(url);
    const pathParts = urlParts.pathname.split('/');
    const handle = pathParts[pathParts.indexOf('products') + 1] || 'sample-product';
    
    // Fetch the actual Shopify product data
    const productUrl = `${urlParts.origin}/products/${handle}.js`;
    
    try {
      const response = await fetch(productUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const productData = await response.json();
      
      // Extract and format the data from Shopify's product JSON
      const variants = productData.variants?.map((variant: any, index: number) => ({
        id: variant.id?.toString() || `var-${index}`,
        title: variant.title || 'Default Title',
        price: (variant.price / 100).toFixed(2), // Shopify returns price in cents
        sku: variant.sku || '',
        inventory_quantity: variant.inventory_quantity || 0,
        weight: variant.weight || 0,
        option1: variant.option1 || null,
        option2: variant.option2 || null,
        option3: variant.option3 || null
      })) || [];
      
      const images = productData.images?.map((img: string) => {
        // Ensure we have full URLs
        return img.startsWith('//') ? `https:${img}` : 
               img.startsWith('/') ? `${urlParts.origin}${img}` : img;
      }) || [];
      
      return {
        handle: productData.handle || handle,
        title: productData.title || `Product ${handle}`,
        body_html: productData.description || '<p>No description available.</p>',
        vendor: productData.vendor || 'Unknown Vendor',
        type: productData.type || 'General',
        tags: productData.tags || [],
        published: productData.available !== false,
        images: images,
        variants: variants,
        price: variants.length > 0 ? variants[0].price : '0.00',
        compare_at_price: productData.compare_at_price ? (productData.compare_at_price / 100).toFixed(2) : undefined
      };
      
    } catch (fetchError) {
      console.warn('Failed to fetch real product data, using fallback:', fetchError);
      
      // Fallback to scraping the HTML page
      const htmlResponse = await fetch(url);
      if (!htmlResponse.ok) {
        throw new Error(`Failed to fetch product page: ${htmlResponse.status}`);
      }
      
      const html = await htmlResponse.text();
      
      // Extract basic data from HTML using regex patterns
      const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
      const title = titleMatch ? titleMatch[1].replace(/ \| .*/g, '').trim() : `Product ${handle}`;
      
      // Look for JSON-LD structured data
      const jsonLdMatch = html.match(/<script[^>]*type=["']application\/ld\+json["'][^>]*>(.*?)<\/script>/is);
      let structuredData = null;
      if (jsonLdMatch) {
        try {
          structuredData = JSON.parse(jsonLdMatch[1]);
        } catch (e) {
          console.warn('Failed to parse structured data');
        }
      }
      
      // Extract images from various possible sources
      const images: string[] = [];
      
      // Try to find product images in the HTML
      const imgMatches = html.match(/<img[^>]+src=["']([^"']*product[^"']*)["'][^>]*>/gi) || [];
      imgMatches.forEach(imgTag => {
        const srcMatch = imgTag.match(/src=["']([^"']*)["']/);
        if (srcMatch) {
          let imgSrc = srcMatch[1];
          if (imgSrc.startsWith('//')) imgSrc = `https:${imgSrc}`;
          else if (imgSrc.startsWith('/')) imgSrc = `${urlParts.origin}${imgSrc}`;
          if (!images.includes(imgSrc)) images.push(imgSrc);
        }
      });
      
      // Extract price information
      let price = '0.00';
      const priceMatch = html.match(/["']price["']\s*:\s*(\d+)/i) || 
                        html.match(/\$(\d+\.?\d*)/);
      if (priceMatch) {
        price = priceMatch[1].includes('.') ? priceMatch[1] : (parseFloat(priceMatch[1]) / 100).toFixed(2);
      }
      
      return {
        handle: handle,
        title: title,
        body_html: structuredData?.description || '<p>Product description extracted from page.</p>',
        vendor: structuredData?.brand?.name || 'Unknown Vendor',
        type: 'General',
        tags: [],
        published: true,
        images: images.slice(0, 5), // Limit to first 5 images
        variants: [{
          id: 'default-var',
          title: 'Default Title',
          price: price,
          sku: handle.toUpperCase(),
          inventory_quantity: 1,
          weight: 300
        }],
        price: price,
        compare_at_price: undefined
      };
    }
    
  } catch (error) {
    console.error('Error fetching product data:', error);
    
    // Final fallback with extracted handle
    const urlParts = new URL(url);
    const pathParts = urlParts.pathname.split('/');
    const handle = pathParts[pathParts.indexOf('products') + 1] || 'sample-product';
    
    return {
      handle: handle,
      title: `Product ${handle}`,
      body_html: '<p>Unable to fetch product data. Please check the URL and try again.</p>',
      vendor: 'Unknown Vendor',
      type: 'General',
      tags: ['error'],
      published: true,
      images: ['https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=800&h=800&fit=crop'],
      variants: [{
        id: 'error-var',
        title: 'Default Title',
        price: '0.00',
        sku: 'ERROR-001',
        inventory_quantity: 0,
        weight: 0
      }],
      price: '0.00',
      compare_at_price: undefined
    };
  }
}

// Simulate API delay
export function simulateApiDelay(ms: number = 2000): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}