// Mock data generator for demo purposes
// This simulates what the backend would return

interface ProductVariant {
  id: string;
  title: string;
  price: string;
  sku?: string;
  inventory_quantity?: number;
  weight?: number;
  option1?: string;
  option2?: string;
  option3?: string;
}

interface ProductData {
  handle: string;
  title: string;
  body_html: string;
  vendor: string;
  type: string;
  tags: string[];
  published: boolean;
  images: string[];
  variants: ProductVariant[];
  price: string;
  compare_at_price?: string;
}

// Check if we're in development mode and can use CORS proxy
const isDevelopment = import.meta.env.DEV;
const CORS_PROXY = 'https://api.allorigins.win/raw?url=';

export async function generateMockProductData(url: string): Promise<ProductData> {
  try {
    // Extract product handle from URL
    const urlParts = new URL(url);
    const pathParts = urlParts.pathname.split('/');
    const handle = pathParts[pathParts.indexOf('products') + 1] || 'sample-product';

    console.log('Starting product data extraction for:', url);
    console.log('Extracted handle:', handle);
    console.log('Development mode:', isDevelopment);

    // Use the exact same logic as the working test-cors.html page
    let productData = null;
    let method = 'unknown';

    // Try direct fetch first
    try {
      const directUrl = `${urlParts.origin}/products/${handle}.js`;
      console.log('Trying direct fetch:', directUrl);

      const response = await fetch(directUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (response.ok) {
        productData = await response.json();
        method = 'direct';
        console.log('Direct fetch successful');
      } else {
        throw new Error(`Direct fetch failed: ${response.status}`);
      }
    } catch (directError) {
      console.log('Direct fetch failed:', directError.message);

      // Try CORS proxy (same as test-cors.html)
      try {
        const proxyUrl = `${CORS_PROXY}${encodeURIComponent(urlParts.origin + '/products/' + handle + '.js')}`;
        console.log('Trying CORS proxy:', proxyUrl);

        const proxyResponse = await fetch(proxyUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });

        if (proxyResponse.ok) {
          productData = await proxyResponse.json();
          method = 'proxy';
          console.log('CORS proxy successful');
        } else {
          throw new Error(`CORS proxy failed: ${proxyResponse.status}`);
        }
      } catch (proxyError) {
        throw new Error(`Both direct and proxy methods failed. Direct: ${directError.message}, Proxy: ${proxyError.message}`);
      }
    }

    if (!productData) {
      throw new Error('No product data received');
    }

    // Process the data exactly like the working test page
    console.log('Successfully fetched product data:', {
      title: productData.title,
      price: productData.price,
      vendor: productData.vendor,
      images: productData.images?.length || 0,
      variants: productData.variants?.length || 0,
      method: method
    });

    // Extract and format the data from Shopify's product JSON
    const variants = productData.variants?.map((variant: any, index: number) => ({
      id: variant.id?.toString() || `var-${index}`,
      title: variant.title || 'Default Title',
      price: typeof variant.price === 'number' ? (variant.price / 100).toFixed(2) :
             typeof variant.price === 'string' ? variant.price : '0.00',
      sku: variant.sku || '',
      inventory_quantity: variant.inventory_quantity || 0,
      weight: variant.weight || 0,
      option1: variant.option1 || null,
      option2: variant.option2 || null,
      option3: variant.option3 || null
    })) || [];

    // Process images with better URL handling
    const images = productData.images?.map((img: string) => {
      let imageUrl = img;

      // Handle different URL formats
      if (imageUrl.startsWith('//')) {
        imageUrl = `https:${imageUrl}`;
      } else if (imageUrl.startsWith('/')) {
        imageUrl = `${urlParts.origin}${imageUrl}`;
      }

      // Remove size parameters to get high-quality images
      imageUrl = imageUrl.replace(/(_\d+x\d*|_\d*x\d+|_compact|_grande|_large|_medium|_small|_thumb)\.(jpg|jpeg|png|gif|webp)/i, '.$2');

      return imageUrl;
    }) || [];

    // Process price
    const mainPrice = typeof productData.price === 'number' ?
      (productData.price / 100).toFixed(2) :
      variants.length > 0 ? variants[0].price : '0.00';

    const compareAtPrice = productData.compare_at_price && typeof productData.compare_at_price === 'number' ?
      (productData.compare_at_price / 100).toFixed(2) : undefined;

    const result = {
      handle: productData.handle || handle,
      title: productData.title || `Product ${handle}`,
      body_html: productData.description || '<p>No description available.</p>',
      vendor: productData.vendor || 'Unknown Vendor',
      type: productData.type || 'General',
      tags: Array.isArray(productData.tags) ? productData.tags : [],
      published: productData.available !== false,
      images: images.filter(img => img && img.length > 0),
      variants: variants.length > 0 ? variants : [{
        id: 'default-var',
        title: 'Default Title',
        price: mainPrice,
        sku: handle.toUpperCase(),
        inventory_quantity: 1,
        weight: 0
      }],
      price: mainPrice,
      compare_at_price: compareAtPrice
    };

    console.log('Final processed result:', {
      title: result.title,
      price: result.price,
      vendor: result.vendor,
      imageCount: result.images.length,
      variantCount: result.variants.length
    });

    return result;

  } catch (error) {
    console.error('Main application scraping failed:', error);

    // Re-throw the error to let the main application handle it
    // This matches the test page behavior where errors are properly displayed
    throw error;
  }
}

// Note: HTML parsing functions removed - using same logic as test-cors.html
// which only uses the JSON endpoint with CORS proxy fallback

// Simulate API delay
export function simulateApiDelay(ms: number = 2000): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}