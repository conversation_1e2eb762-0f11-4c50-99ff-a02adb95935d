// Simple test script to verify scraping functionality
// This can be run in the browser console to test the scraping logic

async function testScraping() {
  const testUrl = "https://olympuslondon.com/products/amy";
  
  console.log("Testing scraping with URL:", testUrl);
  
  try {
    // Extract product handle from URL
    const urlParts = new URL(testUrl);
    const pathParts = urlParts.pathname.split('/');
    const handle = pathParts[pathParts.indexOf('products') + 1] || 'sample-product';
    
    console.log("Extracted handle:", handle);
    
    // Try Shopify product JSON endpoint
    const productUrl = `${urlParts.origin}/products/${handle}.js`;
    console.log("Trying JSON endpoint:", productUrl);
    
    try {
      const response = await fetch(productUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      if (response.ok) {
        const productData = await response.json();
        console.log("Successfully fetched product data from .js endpoint:");
        console.log("Title:", productData.title);
        console.log("Price:", productData.price);
        console.log("Images:", productData.images?.length || 0);
        console.log("Variants:", productData.variants?.length || 0);
        console.log("Full data:", productData);
        return productData;
      } else {
        console.log("JSON endpoint failed with status:", response.status);
      }
    } catch (jsonError) {
      console.log("JSON endpoint error:", jsonError);
    }
    
    // Try HTML scraping as fallback
    console.log("Trying HTML scraping...");
    const htmlResponse = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    if (htmlResponse.ok) {
      const html = await htmlResponse.text();
      console.log("HTML fetched, length:", html.length);
      
      // Look for product JSON in script tags
      const productJsonRegex = /"product":\s*({[^}]+})/;
      const productJsonMatch = html.match(productJsonRegex);
      
      if (productJsonMatch) {
        console.log("Found product JSON in script tag");
        try {
          const productJson = JSON.parse(productJsonMatch[1]);
          console.log("Parsed product JSON:", productJson);
          return productJson;
        } catch (e) {
          console.log("Failed to parse product JSON from script tag");
        }
      }
      
      // Look for JSON-LD structured data
      const jsonLdRegex = /<script[^>]*type=["']application\/ld\+json["'][^>]*>(.*?)<\/script>/gis;
      let jsonLdMatch;
      
      while ((jsonLdMatch = jsonLdRegex.exec(html)) !== null) {
        try {
          const structuredData = JSON.parse(jsonLdMatch[1]);
          if (structuredData['@type'] === 'Product' || 
              (Array.isArray(structuredData) && structuredData.some(item => item['@type'] === 'Product'))) {
            console.log("Found product data in JSON-LD:", structuredData);
            return structuredData;
          }
        } catch (e) {
          console.log("Failed to parse JSON-LD data");
        }
      }
      
      // Extract basic info manually
      const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
      const title = titleMatch ? titleMatch[1].replace(/\s*\|\s*.*$/g, '').trim() : `Product ${handle}`;
      
      console.log("Extracted title from HTML:", title);
      
      // Look for images
      const imageMatches = html.match(/<img[^>]+src=["']([^"']*product[^"']*)["'][^>]*>/gi) || [];
      console.log("Found", imageMatches.length, "potential product images");
      
      return {
        title: title,
        handle: handle,
        images: imageMatches.slice(0, 5),
        source: 'html_parsing'
      };
    }
    
  } catch (error) {
    console.error("Test failed:", error);
  }
}

// Run the test
console.log("Starting scraping test...");
testScraping().then(result => {
  console.log("Test completed. Result:", result);
}).catch(error => {
  console.error("Test error:", error);
});
