import { useState } from "react";
import { Header } from "@/components/Header";
import { ShopifyUrlForm } from "@/components/ShopifyUrlForm";
import { LoadingState } from "@/components/LoadingState";
import { ProductResults } from "@/components/ProductResults";
import { generateMockProductData, simulateApiDelay } from "@/utils/mockProductData";
import { useToast } from "@/hooks/use-toast";
import wizardHero from "@/assets/wizard-hero.png";
import { Sparkles } from "lucide-react";

type AppState = 'idle' | 'loading' | 'results' | 'error';
type LoadingStage = 'fetching' | 'parsing' | 'processing' | 'generating';

const Index = () => {
  const [state, setState] = useState<AppState>('idle');
  const [loadingStage, setLoadingStage] = useState<LoadingStage>('fetching');
  const [productData, setProductData] = useState<any>(null);
  const { toast } = useToast();

  const handleUrlSubmit = async (url: string) => {
    setState('loading');
    setLoadingStage('fetching');
    
    try {
      // Stage 1: Fetching
      await simulateApiDelay(1500);
      setLoadingStage('parsing');
      
      // Stage 2: Parsing
      await simulateApiDelay(1000);
      setLoadingStage('processing');
      
      // Stage 3: Processing
      await simulateApiDelay(800);
      setLoadingStage('generating');
      
      // Stage 4: Generating
      await simulateApiDelay(700);
      
      // Generate real data based on URL
      console.log('About to call generateMockProductData with URL:', url);
      const data = await generateMockProductData(url);
      console.log('Received data from generateMockProductData:', {
        title: data.title,
        vendor: data.vendor,
        price: data.price,
        imageCount: data.images?.length,
        variantCount: data.variants?.length,
        tags: data.tags
      });
      setProductData(data);
      setState('results');
      
      toast({
        title: "Success!",
        description: "Product data extracted successfully",
        variant: "default",
      });
      
    } catch (error) {
      console.error('Main application error:', error);
      setState('error');

      // Show detailed error information to help with debugging
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      toast({
        title: "Scraping Failed",
        description: `Unable to extract product data: ${errorMessage}`,
        variant: "destructive",
      });
    }
  };

  const handleReset = () => {
    setState('idle');
    setProductData(null);
    setLoadingStage('fetching');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {state === 'idle' && (
          <div className="space-y-8 animate-fade-in">
            {/* Hero Section */}
            <div className="text-center space-y-6 py-8">
              <div className="relative inline-block">
                <img 
                  src={wizardHero} 
                  alt="Shopify Scraper Wizard" 
                  className="w-64 h-36 object-contain mx-auto"
                />
                <Sparkles className="absolute -top-2 -right-2 h-8 w-8 text-primary animate-pulse-glow" />
              </div>
              <div className="space-y-3">
                <h1 className="text-4xl md:text-5xl font-bold bg-gradient-primary bg-clip-text text-transparent">
                  Shopify Scraper Wizard
                </h1>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Transform any Shopify product page into a CSV file ready for import. 
                  Extract product data, variants, and images with just one click.
                </p>
              </div>
            </div>

            {/* Form */}
            <ShopifyUrlForm onSubmit={handleUrlSubmit} isLoading={false} />

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-16">
              <div className="text-center p-6 bg-gradient-card rounded-lg border border-border/50 shadow-card">
                <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold text-foreground mb-2">Smart Extraction</h3>
                <p className="text-sm text-muted-foreground">
                  Automatically extracts all product data including variants, pricing, and images
                </p>
              </div>
              
              <div className="text-center p-6 bg-gradient-card rounded-lg border border-border/50 shadow-card">
                <div className="w-12 h-12 bg-accent/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="h-6 w-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-foreground mb-2">Shopify Ready</h3>
                <p className="text-sm text-muted-foreground">
                  CSV files formatted perfectly for direct import into Shopify admin
                </p>
              </div>
              
              <div className="text-center p-6 bg-gradient-card rounded-lg border border-border/50 shadow-card">
                <div className="w-12 h-12 bg-success/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="h-6 w-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-foreground mb-2">Lightning Fast</h3>
                <p className="text-sm text-muted-foreground">
                  Process products in seconds with our optimized scraping engine
                </p>
              </div>
            </div>
          </div>
        )}

        {state === 'loading' && (
          <LoadingState stage={loadingStage} />
        )}

        {state === 'results' && productData && (
          <ProductResults productData={productData} onReset={handleReset} />
        )}

        {state === 'error' && (
          <div className="text-center py-16 animate-fade-in">
            <div className="space-y-4">
              <div className="w-16 h-16 bg-destructive/20 rounded-full flex items-center justify-center mx-auto">
                <svg className="h-8 w-8 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-foreground">Something went wrong</h2>
              <p className="text-muted-foreground">
                We couldn't extract the product data. Please check the URL and try again.
              </p>
              <button
                onClick={handleReset}
                className="mt-4 px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default Index;
