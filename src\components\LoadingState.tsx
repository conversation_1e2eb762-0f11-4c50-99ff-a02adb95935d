import { Card, CardContent } from "@/components/ui/card";
import { Loader2, Search, Database, FileText, CheckCircle } from "lucide-react";

interface LoadingStateProps {
  stage: 'fetching' | 'parsing' | 'processing' | 'generating';
}

const stages = {
  fetching: {
    icon: Search,
    title: "Fetching Product Page",
    description: "Connecting to Shopify store and retrieving product data..."
  },
  parsing: {
    icon: Database,
    title: "Parsing Product Data",
    description: "Extracting product information, variants, and images..."
  },
  processing: {
    icon: FileText,
    title: "Processing Information",
    description: "Organizing data and preparing for CSV export..."
  },
  generating: {
    icon: CheckCircle,
    title: "Generating CSV",
    description: "Creating Shopify-compatible CSV file..."
  }
};

export function LoadingState({ stage }: LoadingStateProps) {
  const currentStage = stages[stage];
  const Icon = currentStage.icon;
  
  return (
    <Card className="w-full max-w-2xl mx-auto bg-gradient-card border-border/50 shadow-card animate-fade-in">
      <CardContent className="p-8 text-center space-y-6">
        <div className="relative">
          <div className="w-20 h-20 mx-auto bg-gradient-primary rounded-full flex items-center justify-center shadow-glow animate-pulse-glow">
            <Icon className="h-10 w-10 text-primary-foreground" />
          </div>
          <Loader2 className="absolute top-0 left-1/2 transform -translate-x-1/2 w-20 h-20 text-primary animate-spin opacity-30" />
        </div>
        
        <div className="space-y-2">
          <h3 className="text-xl font-semibold text-foreground">{currentStage.title}</h3>
          <p className="text-muted-foreground">{currentStage.description}</p>
        </div>
        
        <div className="w-full bg-muted rounded-full h-2">
          <div 
            className="bg-gradient-primary h-2 rounded-full transition-all duration-1000 ease-out"
            style={{ 
              width: stage === 'fetching' ? '25%' : 
                     stage === 'parsing' ? '50%' : 
                     stage === 'processing' ? '75%' : '100%' 
            }}
          />
        </div>
        
        <div className="flex justify-center space-x-4 text-xs text-muted-foreground">
          {Object.entries(stages).map(([key, stageInfo], index) => (
            <div 
              key={key}
              className={`flex items-center gap-1 ${
                key === stage ? 'text-primary' : 
                Object.keys(stages).indexOf(stage) > index ? 'text-success' : ''
              }`}
            >
              <div className={`w-2 h-2 rounded-full ${
                key === stage ? 'bg-primary animate-pulse' : 
                Object.keys(stages).indexOf(stage) > index ? 'bg-success' : 'bg-muted-foreground'
              }`} />
              <span className="hidden sm:inline">{stageInfo.title}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}