// Test script to verify the main application integration
// This simulates what the main application should do

// Import the exact same CORS proxy logic
const CORS_PROXY = 'https://api.allorigins.win/raw?url=';
const isDevelopment = true; // Simulating development mode

async function testMainAppIntegration() {
  const url = "https://olympuslondon.com/products/oxfords-genuine-leather-sneakers";
  
  console.log("Testing main application integration with URL:", url);
  
  try {
    // Extract product handle from URL (same as main app)
    const urlParts = new URL(url);
    const pathParts = urlParts.pathname.split('/');
    const handle = pathParts[pathParts.indexOf('products') + 1] || 'sample-product';
    
    console.log('Starting product data extraction for:', url);
    console.log('Extracted handle:', handle);
    console.log('Development mode:', isDevelopment);
    
    // Use the exact same logic as the updated main application
    let productData = null;
    let method = 'unknown';
    
    // Try direct fetch first
    try {
      const directUrl = `${urlParts.origin}/products/${handle}.js`;
      console.log('Trying direct fetch:', directUrl);
      
      const response = await fetch(directUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      if (response.ok) {
        productData = await response.json();
        method = 'direct';
        console.log('Direct fetch successful');
      } else {
        throw new Error(`Direct fetch failed: ${response.status}`);
      }
    } catch (directError) {
      console.log('Direct fetch failed:', directError.message);
      
      // Try CORS proxy (same as test-cors.html)
      try {
        const proxyUrl = `${CORS_PROXY}${encodeURIComponent(urlParts.origin + '/products/' + handle + '.js')}`;
        console.log('Trying CORS proxy:', proxyUrl);
        
        const proxyResponse = await fetch(proxyUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });
        
        if (proxyResponse.ok) {
          productData = await proxyResponse.json();
          method = 'proxy';
          console.log('CORS proxy successful');
        } else {
          throw new Error(`CORS proxy failed: ${proxyResponse.status}`);
        }
      } catch (proxyError) {
        throw new Error(`Both direct and proxy methods failed. Direct: ${directError.message}, Proxy: ${proxyError.message}`);
      }
    }
    
    if (!productData) {
      throw new Error('No product data received');
    }
    
    // Process the data exactly like the updated main application
    console.log('Successfully fetched product data:', {
      title: productData.title,
      price: productData.price,
      vendor: productData.vendor,
      images: productData.images?.length || 0,
      variants: productData.variants?.length || 0,
      method: method
    });
    
    // Extract and format the data from Shopify's product JSON
    const variants = productData.variants?.map((variant, index) => ({
      id: variant.id?.toString() || `var-${index}`,
      title: variant.title || 'Default Title',
      price: typeof variant.price === 'number' ? (variant.price / 100).toFixed(2) : 
             typeof variant.price === 'string' ? variant.price : '0.00',
      sku: variant.sku || '',
      inventory_quantity: variant.inventory_quantity || 0,
      weight: variant.weight || 0,
      option1: variant.option1 || null,
      option2: variant.option2 || null,
      option3: variant.option3 || null
    })) || [];
    
    // Process images with better URL handling
    const images = productData.images?.map((img) => {
      let imageUrl = img;
      
      // Handle different URL formats
      if (imageUrl.startsWith('//')) {
        imageUrl = `https:${imageUrl}`;
      } else if (imageUrl.startsWith('/')) {
        imageUrl = `${urlParts.origin}${imageUrl}`;
      }
      
      // Remove size parameters to get high-quality images
      imageUrl = imageUrl.replace(/(_\d+x\d*|_\d*x\d+|_compact|_grande|_large|_medium|_small|_thumb)\.(jpg|jpeg|png|gif|webp)/i, '.$2');
      
      return imageUrl;
    }) || [];
    
    // Process price
    const mainPrice = typeof productData.price === 'number' ? 
      (productData.price / 100).toFixed(2) : 
      variants.length > 0 ? variants[0].price : '0.00';
    
    const compareAtPrice = productData.compare_at_price && typeof productData.compare_at_price === 'number' ? 
      (productData.compare_at_price / 100).toFixed(2) : undefined;
    
    const result = {
      handle: productData.handle || handle,
      title: productData.title || `Product ${handle}`,
      body_html: productData.description || '<p>No description available.</p>',
      vendor: productData.vendor || 'Unknown Vendor',
      type: productData.type || 'General',
      tags: Array.isArray(productData.tags) ? productData.tags : [],
      published: productData.available !== false,
      images: images.filter(img => img && img.length > 0),
      variants: variants.length > 0 ? variants : [{
        id: 'default-var',
        title: 'Default Title',
        price: mainPrice,
        sku: handle.toUpperCase(),
        inventory_quantity: 1,
        weight: 0
      }],
      price: mainPrice,
      compare_at_price: compareAtPrice
    };
    
    console.log('Final processed result:', {
      title: result.title,
      price: result.price,
      vendor: result.vendor,
      imageCount: result.images.length,
      variantCount: result.variants.length
    });
    
    console.log('\n=== MAIN APPLICATION SHOULD SHOW ===');
    console.log('Title:', result.title);
    console.log('Price: £' + result.price);
    console.log('Compare at price: £' + result.compare_at_price);
    console.log('Vendor:', result.vendor);
    console.log('Type:', result.type);
    console.log('Images:', result.images.length);
    console.log('Variants:', result.variants.length);
    console.log('Method used:', method);
    
    return result;
    
  } catch (error) {
    console.error('Main application integration test failed:', error);
    throw error;
  }
}

// Run the test
console.log("Testing main application integration...");
testMainAppIntegration().then(result => {
  console.log("\n✅ Integration test completed successfully!");
  console.log("The main application should now show the exact same data as the test page.");
}).catch(error => {
  console.error("\n❌ Integration test failed:", error.message);
});
