<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test - Shopify Scraper</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { border-color: #4CAF50; background-color: #f8fff8; }
        .error { border-color: #f44336; background-color: #fff8f8; }
        .loading { border-color: #2196F3; background-color: #f8f8ff; }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #45a049; }
        button:disabled { background-color: #cccccc; cursor: not-allowed; }
        pre {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Shopify Scraper CORS Test</h1>
        <p>This page tests the CORS proxy functionality for scraping Shopify product data.</p>
        
        <div class="test-section">
            <h3>Test URLs</h3>
            <button onclick="testUrl('https://olympuslondon.com/products/amy')">Test Amy Product</button>
            <button onclick="testUrl('https://olympuslondon.com/products/novasneakers')">Test Nova Sneakers</button>
            <button onclick="testCustomUrl()">Test Custom URL</button>
            <br><br>
            <input type="text" id="customUrl" placeholder="Enter Shopify product URL..." style="width: 400px; padding: 8px;">
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const CORS_PROXY = 'https://api.allorigins.win/raw?url=';
        
        async function testUrl(url) {
            const resultsDiv = document.getElementById('results');
            const testId = Date.now();
            
            // Create test section
            const testSection = document.createElement('div');
            testSection.className = 'test-section loading';
            testSection.id = `test-${testId}`;
            testSection.innerHTML = `
                <h4>Testing: ${url}</h4>
                <div class="result">Loading...</div>
            `;
            resultsDiv.appendChild(testSection);
            
            try {
                // Extract handle
                const urlParts = new URL(url);
                const pathParts = urlParts.pathname.split('/');
                const handle = pathParts[pathParts.indexOf('products') + 1];
                
                console.log('Testing URL:', url);
                console.log('Handle:', handle);
                
                // Test direct fetch first
                let productData = null;
                let method = 'unknown';
                
                try {
                    const directUrl = `${urlParts.origin}/products/${handle}.js`;
                    console.log('Trying direct fetch:', directUrl);
                    
                    const response = await fetch(directUrl);
                    if (response.ok) {
                        productData = await response.json();
                        method = 'direct';
                        console.log('Direct fetch successful');
                    } else {
                        throw new Error(`Direct fetch failed: ${response.status}`);
                    }
                } catch (directError) {
                    console.log('Direct fetch failed:', directError.message);
                    
                    // Try CORS proxy
                    try {
                        const proxyUrl = `${CORS_PROXY}${encodeURIComponent(urlParts.origin + '/products/' + handle + '.js')}`;
                        console.log('Trying CORS proxy:', proxyUrl);
                        
                        const proxyResponse = await fetch(proxyUrl);
                        if (proxyResponse.ok) {
                            productData = await proxyResponse.json();
                            method = 'proxy';
                            console.log('CORS proxy successful');
                        } else {
                            throw new Error(`CORS proxy failed: ${proxyResponse.status}`);
                        }
                    } catch (proxyError) {
                        throw new Error(`Both direct and proxy methods failed. Direct: ${directError.message}, Proxy: ${proxyError.message}`);
                    }
                }
                
                if (productData) {
                    testSection.className = 'test-section success';
                    testSection.querySelector('.result').innerHTML = `
                        <strong>✅ Success (${method})</strong><br>
                        <strong>Title:</strong> ${productData.title}<br>
                        <strong>Price:</strong> £${(productData.price / 100).toFixed(2)}<br>
                        <strong>Vendor:</strong> ${productData.vendor}<br>
                        <strong>Images:</strong> ${productData.images?.length || 0}<br>
                        <strong>Variants:</strong> ${productData.variants?.length || 0}<br>
                        <details>
                            <summary>Raw Data</summary>
                            <pre>${JSON.stringify(productData, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    throw new Error('No product data received');
                }
                
            } catch (error) {
                console.error('Test failed:', error);
                testSection.className = 'test-section error';
                testSection.querySelector('.result').innerHTML = `
                    <strong>❌ Failed</strong><br>
                    <strong>Error:</strong> ${error.message}<br>
                    <em>This is likely due to CORS restrictions. The scraper would work in a server-side environment.</em>
                `;
            }
        }
        
        function testCustomUrl() {
            const customUrl = document.getElementById('customUrl').value.trim();
            if (customUrl) {
                testUrl(customUrl);
            } else {
                alert('Please enter a URL');
            }
        }
        
        // Allow Enter key in custom URL input
        document.getElementById('customUrl').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testCustomUrl();
            }
        });
    </script>
</body>
</html>
