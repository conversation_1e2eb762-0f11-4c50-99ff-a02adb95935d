// Test script to verify the Oxford leather trainers scraping
// Run with: node test-oxford-scraping.js

async function testOxfordScraping() {
  const url = "https://olympuslondon.com/products/oxfords-genuine-leather-sneakers";
  
  console.log("Testing Oxford leather trainers scraping with URL:", url);
  
  try {
    // Extract product handle from URL
    const urlParts = new URL(url);
    const pathParts = urlParts.pathname.split('/');
    const handle = pathParts[pathParts.indexOf('products') + 1] || 'sample-product';
    
    console.log("Extracted handle:", handle);
    
    // Try Shopify product JSON endpoint
    const productUrl = `${urlParts.origin}/products/${handle}.js`;
    console.log("Attempting to fetch from JSON endpoint:", productUrl);
    
    try {
      const response = await fetch(productUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      console.log("JSON endpoint response status:", response.status);
      
      if (response.ok) {
        const productData = await response.json();
        console.log("Successfully fetched product data from .js endpoint:");
        console.log("Title:", productData.title);
        console.log("Price:", productData.price, "cents (£" + (productData.price / 100).toFixed(2) + ")");
        console.log("Compare at price:", productData.compare_at_price, "cents (£" + (productData.compare_at_price / 100).toFixed(2) + ")");
        console.log("Vendor:", productData.vendor);
        console.log("Type:", productData.type);
        console.log("Images:", productData.images?.length || 0);
        console.log("Variants:", productData.variants?.length || 0);
        console.log("Available:", productData.available);
        console.log("Tags:", productData.tags);
        
        // Show first few variants
        if (productData.variants && productData.variants.length > 0) {
          console.log("\nFirst 3 variants:");
          productData.variants.slice(0, 3).forEach((variant, index) => {
            console.log(`  ${index + 1}. ${variant.title} - £${(variant.price / 100).toFixed(2)} (${variant.available ? 'Available' : 'Out of stock'})`);
          });
        }
        
        // Show first few images
        if (productData.images && productData.images.length > 0) {
          console.log("\nFirst 3 images:");
          productData.images.slice(0, 3).forEach((image, index) => {
            console.log(`  ${index + 1}. ${image}`);
          });
        }
        
        // Test the data processing like our application does
        console.log("\n=== Testing Application Data Processing ===");
        
        const variants = productData.variants?.map((variant, index) => ({
          id: variant.id?.toString() || `var-${index}`,
          title: variant.title || 'Default Title',
          price: typeof variant.price === 'number' ? (variant.price / 100).toFixed(2) : 
                 typeof variant.price === 'string' ? variant.price : '0.00',
          sku: variant.sku || '',
          inventory_quantity: variant.inventory_quantity || 0,
          weight: variant.weight || 0,
          option1: variant.option1 || null,
          option2: variant.option2 || null,
          option3: variant.option3 || null
        })) || [];
        
        console.log("Processed variants:", variants.length);
        
        // Process images
        const images = productData.images?.map((img) => {
          let imageUrl = img;
          
          // Handle different URL formats
          if (imageUrl.startsWith('//')) {
            imageUrl = `https:${imageUrl}`;
          } else if (imageUrl.startsWith('/')) {
            imageUrl = `${urlParts.origin}${imageUrl}`;
          }
          
          // Remove size parameters to get high-quality images
          imageUrl = imageUrl.replace(/(_\d+x\d*|_\d*x\d+|_compact|_grande|_large|_medium|_small|_thumb)\.(jpg|jpeg|png|gif|webp)/i, '.$2');
          
          return imageUrl;
        }) || [];
        
        console.log("Processed images:", images.length);
        
        // Process price
        const mainPrice = typeof productData.price === 'number' ? 
          (productData.price / 100).toFixed(2) : 
          variants.length > 0 ? variants[0].price : '0.00';
        
        const compareAtPrice = productData.compare_at_price && typeof productData.compare_at_price === 'number' ? 
          (productData.compare_at_price / 100).toFixed(2) : undefined;
        
        const result = {
          handle: productData.handle || handle,
          title: productData.title || `Product ${handle}`,
          body_html: productData.description || '<p>No description available.</p>',
          vendor: productData.vendor || 'Unknown Vendor',
          type: productData.type || 'General',
          tags: Array.isArray(productData.tags) ? productData.tags : [],
          published: productData.available !== false,
          images: images.filter(img => img && img.length > 0),
          variants: variants.length > 0 ? variants : [{
            id: 'default-var',
            title: 'Default Title',
            price: mainPrice,
            sku: handle.toUpperCase(),
            inventory_quantity: 1,
            weight: 0
          }],
          price: mainPrice,
          compare_at_price: compareAtPrice
        };
        
        console.log("\n=== Final Application Result ===");
        console.log("Title:", result.title);
        console.log("Price:", result.price);
        console.log("Compare at price:", result.compare_at_price);
        console.log("Vendor:", result.vendor);
        console.log("Type:", result.type);
        console.log("Images:", result.images.length);
        console.log("Variants:", result.variants.length);
        console.log("Published:", result.published);
        console.log("Tags:", result.tags);
        
        return result;
        
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (jsonError) {
      console.error("Failed to fetch from .js endpoint:", jsonError);
      throw jsonError;
    }
    
  } catch (error) {
    console.error("Test failed:", error);
    return null;
  }
}

// Run the test
console.log("Starting Oxford leather trainers scraping test...");
testOxfordScraping().then(result => {
  if (result) {
    console.log("\n✅ Test completed successfully!");
    console.log("This data should match what appears in the main application.");
  } else {
    console.log("\n❌ Test failed!");
  }
}).catch(error => {
  console.error("❌ Test error:", error);
});
