@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 240 5% 6%;
    --foreground: 240 5% 95%;

    --card: 240 4% 9%;
    --card-foreground: 240 5% 95%;

    --popover: 240 4% 9%;
    --popover-foreground: 240 5% 95%;

    --primary: 260 95% 65%;
    --primary-foreground: 240 5% 6%;

    --secondary: 240 5% 15%;
    --secondary-foreground: 240 5% 95%;

    --muted: 240 5% 15%;
    --muted-foreground: 240 4% 60%;

    --accent: 200 95% 55%;
    --accent-foreground: 240 5% 6%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 240 5% 95%;

    --border: 240 6% 20%;
    --input: 240 6% 20%;
    --ring: 260 95% 65%;

    --success: 142 76% 55%;
    --success-foreground: 240 5% 6%;

    --warning: 38 92% 50%;
    --warning-foreground: 240 5% 6%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    --gradient-card: linear-gradient(145deg, hsl(var(--card)), hsl(240 4% 12%));
    --gradient-button: linear-gradient(135deg, hsl(var(--primary)), hsl(280 95% 70%));

    /* Shadows */
    --shadow-glow: 0 0 40px hsl(var(--primary) / 0.3);
    --shadow-card: 0 10px 30px -10px hsl(var(--primary) / 0.2);
    --shadow-button: 0 4px 15px hsl(var(--primary) / 0.4);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}